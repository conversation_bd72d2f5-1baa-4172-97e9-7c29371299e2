#!/usr/bin/env python3

import sys
import os

# Add the solution directory to the path
sys.path.insert(0, 'solution')

# Import the main function
from main import solve

# Test with sample input
test_input = """2
2 3 1000
6 5 2000
3
0 60000 3000
10000 50000 2000
0 200000 5000
10 20 20
20 10 12
10 100"""

# Redirect stdin to our test input
import io
sys.stdin = io.StringIO(test_input)

print("Running test...")
try:
    solve()
    print("Test completed successfully!")
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
