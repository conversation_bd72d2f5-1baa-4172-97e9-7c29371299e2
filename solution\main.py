import sys
import math
from queue import PriorityQueue
import bisect
from collections import defaultdict

# --- Data Models ---

class Server:
    def __init__(self, id, g, k, m):
        self.id = id
        self.g = g  # NPU count
        self.k = k  # Speed coefficient
        self.m = m  # Memory size

class Request:
    def __init__(self, user_id, batch_size, arrival_time):
        self.user_id = user_id
        self.batch_size = batch_size
        self.arrival_time = arrival_time
        self.start_time = None
        self.finish_time = None

class NPU:
    def __init__(self, id, server_id, k, m):
        self.id = id
        self.server_id = server_id
        self.k = k
        self.m = m
        # Queue for processing requests (sorted by arrival time, then user_id)
        self.request_queue = []
        # Track memory usage over time: list of (time, memory_delta) events
        self.memory_events = []
        # Current memory usage
        self.current_memory = 0

    def add_request(self, request):
        """Add a request to the NPU's queue."""
        # Insert in sorted order: by arrival_time first, then by user_id
        bisect.insort(self.request_queue, (request.arrival_time, request.user_id, request))

    def get_memory_at_time(self, time):
        """Calculate memory usage at a specific time."""
        memory = 0
        for event_time, delta in self.memory_events:
            if event_time <= time:
                memory += delta
            else:
                break
        return memory

    def find_earliest_start_time(self, arrival_time, mem_needed):
        """Find the earliest time a task can start on this NPU."""
        if mem_needed > self.m:
            return float('inf')

        # Check if we can start immediately at arrival_time
        mem_at_arrival = self.get_memory_at_time(arrival_time)
        if mem_at_arrival + mem_needed <= self.m:
            return arrival_time

        # Find the earliest time when enough memory is available
        current_memory = mem_at_arrival
        for i, (event_time, delta) in enumerate(self.memory_events):
            if event_time <= arrival_time:
                continue

            current_memory += delta
            if current_memory + mem_needed <= self.m:
                return event_time

        return float('inf')

    def schedule_task(self, start_time, finish_time, mem_needed):
        """Schedule a task on this NPU."""
        bisect.insort(self.memory_events, (start_time, mem_needed))
        bisect.insort(self.memory_events, (finish_time, -mem_needed))

class User:
    def __init__(self, id, s, e, cnt):
        self.id = id
        self.s = s  # Start time
        self.e = e  # End time
        self.cnt = cnt  # Total samples
        self.samples_left = cnt
        self.next_send_time = s
        self.requests = []  # List of (send_time, server_id, npu_id, batch_size)
        self.last_server_npu = None  # (server_id, npu_id) for tracking moves

# --- Global Parameters ---

A, B = 0, 0
LATENCIES = []

# --- Utility Functions ---

def calculate_inference_time(batch_size, k):
    """Calculate inference time for a batch."""
    return math.ceil(batch_size / k)

def calculate_memory_needed(batch_size, a, b):
    """Calculate memory needed for a batch."""
    return a * batch_size + b

def get_npu_id_in_server(global_npu_id, servers):
    """Convert global NPU ID to (server_id, npu_id_in_server)."""
    current_id = 0
    for server_id, server in enumerate(servers):
        if current_id <= global_npu_id < current_id + server.g:
            return server_id, global_npu_id - current_id + 1
        current_id += server.g
    return -1, -1

def calculate_urgency(user, current_time):
    """Calculate urgency score for a user based on remaining time and samples."""
    remaining_time = user.e - current_time
    if remaining_time <= 0:
        return float('inf')  # Already past deadline

    samples_per_ms = user.samples_left / remaining_time
    return samples_per_ms

def find_best_batch_size(user, npu, server, send_time, A, B, LATENCIES):
    """Find the optimal batch size for a user on a specific NPU."""
    max_b_for_npu = (server.m - B) // A
    if max_b_for_npu <= 0:
        return None

    latency = LATENCIES[server.id][user.id]
    arrival_time = send_time + latency

    # Try batch sizes from largest to smallest
    for batch_size in range(min(user.samples_left, max_b_for_npu), 0, -1):
        mem_needed = calculate_memory_needed(batch_size, A, B)
        start_time = npu.find_earliest_start_time(arrival_time, mem_needed)

        if start_time == float('inf'):
            continue

        inference_time = calculate_inference_time(batch_size, server.k)
        finish_time = start_time + inference_time

        # Check if this request can finish before user's deadline
        if finish_time <= user.e:
            return {
                'batch_size': batch_size,
                'start_time': start_time,
                'finish_time': finish_time,
                'mem_needed': mem_needed
            }

    return None

# --- Scheduler Logic ---

def solve():
    global A, B, LATENCIES

    # 1. Read Input
    try:
        N = int(input().strip())

        servers_data = []
        for _ in range(N):
            servers_data.append(list(map(int, input().split())))

        M = int(input().strip())

        users_data = []
        for _ in range(M):
            users_data.append(list(map(int, input().split())))

        LATENCIES = []
        for _ in range(N):
            LATENCIES.append(list(map(int, input().split())))

        A, B = map(int, input().split())

    except (EOFError, ValueError) as e:
        # Handle empty input for local testing
        return

    # 2. Initialize Models
    servers = [Server(i, g, k, m) for i, (g, k, m) in enumerate(servers_data)]
    users = [User(i, s, e, cnt) for i, (s, e, cnt) in enumerate(users_data)]

    # Create NPUs with proper indexing
    npus = []
    for server in servers:
        for npu_idx in range(server.g):
            npus.append(NPU(len(npus), server.id, server.k, server.m))

    # 3. Improved Event-driven Scheduling with Priority
    user_pq = PriorityQueue()
    for user in users:
        urgency = calculate_urgency(user, user.s)
        user_pq.put((user.next_send_time, urgency, user.id))

    while not user_pq.empty():
        t, urgency, user_id = user_pq.get()
        user = users[user_id]

        if user.samples_left <= 0:
            continue

        send_time = max(t, user.next_send_time)

        # Check if we can still meet the deadline
        if send_time >= user.e:
            continue

        # --- Enhanced Decision Making ---
        best_option = None
        best_cost = float('inf')

        # Sort NPUs by preference (prefer NPUs with less load and better performance)
        npu_candidates = []
        for i, npu in enumerate(npus):
            server = servers[npu.server_id]

            # Calculate NPU load (current memory usage)
            current_load = npu.get_memory_at_time(send_time) / server.m

            # Calculate performance score (higher k is better)
            performance_score = server.k

            # Calculate latency cost
            latency_cost = LATENCIES[server.id][user.id]

            # Combined score (lower is better)
            npu_score = current_load * 100 + latency_cost - performance_score

            npu_candidates.append((npu_score, i, npu, server))

        # Sort by score (best first)
        npu_candidates.sort(key=lambda x: x[0])

        for _, _, npu, server in npu_candidates:
            # Find the best batch size for this NPU
            batch_result = find_best_batch_size(user, npu, server, send_time, A, B, LATENCIES)

            if not batch_result:
                continue

            batch_size = batch_result['batch_size']
            start_time = batch_result['start_time']
            finish_time = batch_result['finish_time']
            mem_needed = batch_result['mem_needed']

            # Enhanced cost function
            cost = finish_time

            # Add urgency factor (more urgent users get priority)
            current_urgency = calculate_urgency(user, send_time)
            cost += 1000 / (current_urgency + 1)  # Higher urgency = lower cost

            # Add penalty for switching servers/NPUs
            current_server_npu = (server.id, npu.id)
            if user.last_server_npu and user.last_server_npu != current_server_npu:
                cost += 100  # Move penalty

            # Prefer larger batch sizes (efficiency bonus)
            cost -= batch_size * 0.5

            # Prefer NPUs with better performance
            cost -= server.k * 2

            # If this option is better, update best_option
            if cost < best_cost:
                server_id, npu_id_in_server = get_npu_id_in_server(npu.id, servers)
                best_cost = cost
                best_option = {
                    "npu": npu,
                    "batch_size": batch_size,
                    "server_id": server_id,
                    "npu_id_in_server": npu_id_in_server,
                    "start_time": start_time,
                    "finish_time": finish_time,
                    "mem_needed": mem_needed
                }

        # --- Commit to the best decision ---
        if best_option:
            chosen_npu = best_option["npu"]
            batch = best_option["batch_size"]
            start_time = best_option["start_time"]
            finish_time = best_option["finish_time"]
            mem_needed = best_option["mem_needed"]
            server_id = best_option["server_id"]
            npu_id_in_server = best_option["npu_id_in_server"]

            # Schedule the task on the NPU
            chosen_npu.schedule_task(start_time, finish_time, mem_needed)

            # Record the decision for the user (1-indexed for output)
            user.requests.append((send_time, server_id + 1, npu_id_in_server, batch))

            # Update user state
            user.samples_left -= batch
            latency = LATENCIES[server_id][user.id]
            user.next_send_time = send_time + latency + 1
            user.last_server_npu = (server_id, chosen_npu.id)

            # Schedule next request if there are samples left and time allows
            if user.samples_left > 0 and user.next_send_time < user.e:
                new_urgency = calculate_urgency(user, user.next_send_time)
                user_pq.put((user.next_send_time, new_urgency, user.id))

    # 4. Print Output
    for user in users:
        # Sort requests by send time to ensure correct order
        user.requests.sort()
        print(len(user.requests))
        if user.requests:
            flat_list = [item for tpl in user.requests for item in tpl]
            print(*flat_list)
        else:
            print()

if __name__ == "__main__":
    solve() 