#!/usr/bin/env python3

import sys
import io

# Test input from the problem statement
test_input = """2
2 3 1000
6 5 2000
3
0 60000 3000
10000 50000 2000
0 200000 5000
10 20 20
20 10 12
10 100"""

# Expected output format validation
def validate_output(output_lines, users_data, servers_data, latencies, a, b):
    """Validate the output format and constraints."""
    errors = []
    
    # Parse servers
    servers = []
    for i, (g, k, m) in enumerate(servers_data):
        servers.append({'id': i, 'g': g, 'k': k, 'm': m})
    
    # Parse users
    users = []
    for i, (s, e, cnt) in enumerate(users_data):
        users.append({'id': i, 's': s, 'e': e, 'cnt': cnt})
    
    line_idx = 0
    for user_id in range(len(users)):
        user = users[user_id]
        
        # Read number of requests
        if line_idx >= len(output_lines):
            errors.append(f"Missing output for user {user_id}")
            continue
            
        try:
            num_requests = int(output_lines[line_idx])
            line_idx += 1
        except ValueError:
            errors.append(f"Invalid number of requests for user {user_id}")
            continue
        
        if num_requests == 0:
            continue
            
        # Read requests
        if line_idx >= len(output_lines):
            errors.append(f"Missing request data for user {user_id}")
            continue
            
        try:
            request_data = list(map(int, output_lines[line_idx].split()))
            line_idx += 1
        except ValueError:
            errors.append(f"Invalid request data for user {user_id}")
            continue
        
        if len(request_data) != 4 * num_requests:
            errors.append(f"Wrong number of request parameters for user {user_id}")
            continue
        
        # Validate each request
        total_samples = 0
        prev_time = -1
        
        for i in range(num_requests):
            time = request_data[4*i]
            server_id = request_data[4*i + 1] - 1  # Convert to 0-indexed
            npu_id = request_data[4*i + 2]
            batch_size = request_data[4*i + 3]
            
            total_samples += batch_size
            
            # Validate time order
            if time <= prev_time:
                errors.append(f"User {user_id}: Invalid time order at request {i}")
            prev_time = time
            
            # Validate time bounds
            if time < user['s'] or time >= user['e']:
                errors.append(f"User {user_id}: Request {i} time {time} outside bounds [{user['s']}, {user['e']})")
            
            # Validate server ID
            if server_id < 0 or server_id >= len(servers):
                errors.append(f"User {user_id}: Invalid server ID {server_id + 1}")
                continue
            
            # Validate NPU ID
            if npu_id < 1 or npu_id > servers[server_id]['g']:
                errors.append(f"User {user_id}: Invalid NPU ID {npu_id} for server {server_id + 1}")
            
            # Validate batch size and memory
            memory_needed = a * batch_size + b
            if memory_needed > servers[server_id]['m']:
                errors.append(f"User {user_id}: Batch size {batch_size} exceeds memory on server {server_id + 1}")
        
        # Check if all samples are processed
        if total_samples != user['cnt']:
            errors.append(f"User {user_id}: Total samples {total_samples} != expected {user['cnt']}")
    
    return errors

# Run the test
sys.path.insert(0, 'solution')
from main import solve

# Capture output
old_stdout = sys.stdout
sys.stdout = captured_output = io.StringIO()

# Redirect stdin
sys.stdin = io.StringIO(test_input)

try:
    solve()
    output = captured_output.getvalue()
    sys.stdout = old_stdout
    
    print("=== OUTPUT ===")
    print(output)
    
    # Parse input for validation
    lines = test_input.strip().split('\n')
    N = int(lines[0])
    servers_data = [list(map(int, lines[i+1].split())) for i in range(N)]
    M = int(lines[N+1])
    users_data = [list(map(int, lines[N+2+i].split())) for i in range(M)]
    latencies = [list(map(int, lines[N+2+M+i].split())) for i in range(N)]
    a, b = map(int, lines[N+2+M+N].split())
    
    # Validate output
    output_lines = output.strip().split('\n')
    errors = validate_output(output_lines, users_data, servers_data, latencies, a, b)
    
    print("\n=== VALIDATION ===")
    if errors:
        print("ERRORS FOUND:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("✓ All validations passed!")
        
except Exception as e:
    sys.stdout = old_stdout
    print(f"Error running solution: {e}")
    import traceback
    traceback.print_exc()
